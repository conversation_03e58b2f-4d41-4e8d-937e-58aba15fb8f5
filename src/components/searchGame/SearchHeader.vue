<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 16:50:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-29 16:53:22
 * @FilePath     : /src/components/searchGame/SearchHeader.vue
 * @Description  : 搜索页面导航条组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 16:50:00
-->

<template>
    <div class="search-header">
        <div class="header-left" @click="handleClose">
            <van-icon name="arrow-left" size="20" />
        </div>
        <div class="header-title">Search</div>
        <div class="header-right"></div>
    </div>
</template>

<script setup lang="ts">
// 定义事件
interface Emits {
    (e: 'close'): void
}

const emit = defineEmits<Emits>()

const handleClose = () => {
    emit('close')
}
</script>

<style lang="scss" scoped>
$bg-secondary: #383838;
$text-primary: #ffffff;

.search-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: $bg-secondary;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    flex-shrink: 0;

    .header-left {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        :deep(.van-icon) {
            color: $text-primary;
        }
    }

    .header-title {
        font-size: 18px;
        font-weight: 600;
        color: $text-primary;
    }

    .header-right {
        width: 40px;
    }
}
</style>

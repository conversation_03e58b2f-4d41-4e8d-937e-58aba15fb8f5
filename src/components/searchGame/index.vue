<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 15:59:30
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-29 16:16:05
 * @FilePath     : /src/components/searchGame/index.vue
 * @Description  : 游戏搜索页面组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 15:59:30
-->

<template>
    <div class="search-game-container">
        <!-- 搜索输入框 -->
        <SearchGameInput v-model="searchValue" @focus="showSearchPopup" />

        <!-- 搜索弹窗 -->
        <van-popup v-model:show="isSearchVisible" position="right" :style="{ width: '100%', height: '100%' }" :close-on-click-overlay="false">
            <div class="search-popup">
                <!-- 头部 -->
                <div class="search-header">
                    <div class="header-left" @click="closeSearchPopup">
                        <van-icon name="arrow-left" size="20" />
                    </div>
                    <div class="header-title">Search</div>
                    <div class="header-right"></div>
                </div>

                <!-- 搜索栏 -->
                <div class="search-bar">
                    <div class="search-input-wrapper">
                        <van-field v-model="searchKeyword" placeholder="Game Name" :border="false" @input="handleSearchInput">
                            <template #left-icon>
                                <span class="category-selector" @click="showCategorySheet = true">
                                    Casino
                                    <van-icon name="arrow-down" size="12" />
                                </span>
                            </template>
                            <template #right-icon>
                                <van-icon name="search" size="18" @click="performSearch" />
                            </template>
                        </van-field>
                    </div>
                </div>

                <!-- 游戏分类标签 -->
                <div class="game-categories">
                    <div
                        v-for="category in gameCategories"
                        :key="category.id"
                        :class="['category-tag', { active: selectedCategory === category.id }]"
                        @click="selectCategory(category.id)"
                    >
                        <van-icon :name="category.icon" size="16" />
                        <span>{{ category.name }}</span>
                    </div>
                </div>

                <!-- 筛选器 -->
                <div class="filters">
                    <div class="filter-item">
                        <span class="filter-label">Sort by:</span>
                        <van-field v-model="sortBy" readonly is-link arrow-direction="down" @click="showSortSheet = true" />
                    </div>
                    <div class="filter-item">
                        <span class="filter-label">Providers:</span>
                        <van-field v-model="provider" readonly is-link arrow-direction="down" @click="showProviderSheet = true" />
                    </div>
                </div>

                <!-- 游戏列表 -->
                <div class="games-grid">
                    <div v-for="game in filteredGames" :key="game.id" class="game-item" @click="selectGame(game)">
                        <div class="game-image">
                            <img :src="game.image" :alt="game.name" />
                            <div v-if="game.tag" :class="['game-tag', game.tag.toLowerCase()]">
                                {{ game.tag }}
                            </div>
                        </div>
                        <div class="game-info">
                            <div class="game-name">{{ game.name }}</div>
                            <div class="game-provider">{{ game.provider }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </van-popup>

        <!-- 分类选择器 -->
        <van-action-sheet v-model:show="showCategorySheet" :actions="categoryActions" @select="onCategorySelect" />

        <!-- 排序选择器 -->
        <van-action-sheet v-model:show="showSortSheet" :actions="sortActions" @select="onSortSelect" />

        <!-- 供应商选择器 -->
        <van-action-sheet v-model:show="showProviderSheet" :actions="providerActions" @select="onProviderSelect" />
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import SearchGameInput from './input.vue'

// 搜索相关状态
const searchValue = ref('')
const searchKeyword = ref('')
const isSearchVisible = ref(false)

// 分类和筛选状态
const selectedCategory = ref('all')
const sortBy = ref('Popular')
const provider = ref('All')

// 弹窗状态
const showCategorySheet = ref(false)
const showSortSheet = ref(false)
const showProviderSheet = ref(false)

// 游戏分类数据
const gameCategories = ref([
    { id: 'all', name: 'All games', icon: 'apps-o' },
    { id: 'original', name: 'Original', icon: 'star-o' },
    { id: 'slots', name: 'Slots', icon: 'gem-o' },
    { id: 'live', name: 'Live', icon: 'video-o' },
    { id: 'lottery', name: 'Lottery', icon: 'gift-o' },
])

// 模拟游戏数据
const gamesList = ref([
    {
        id: 1,
        name: 'LUCKY GAME',
        provider: 'RECTANGLE',
        image: '/api/placeholder/120/120',
        tag: 'HOT',
        category: 'slots',
    },
    {
        id: 2,
        name: 'FORTUNE GEMS',
        provider: 'TADA',
        image: '/api/placeholder/120/120',
        tag: 'NEW',
        category: 'slots',
    },
    {
        id: 3,
        name: 'BINGO',
        provider: 'JILI',
        image: '/api/placeholder/120/120',
        tag: 'NEW',
        category: 'lottery',
    },
    {
        id: 4,
        name: 'SWAGGY GAME',
        provider: 'RECTANGLE',
        image: '/api/placeholder/120/120',
        tag: 'HOT',
        category: 'slots',
    },
    {
        id: 5,
        name: 'MINES2',
        provider: 'AFUN MX',
        image: '/api/placeholder/120/120',
        tag: 'TOP',
        category: 'original',
    },
    {
        id: 6,
        name: 'FORTUNE',
        provider: 'PG SOFT',
        image: '/api/placeholder/120/120',
        tag: 'TOP',
        category: 'slots',
    },
    {
        id: 7,
        name: 'FORTUNE GEMS',
        provider: 'TADA',
        image: '/api/placeholder/120/120',
        tag: 'HOT',
        category: 'slots',
    },
    {
        id: 8,
        name: 'FORTUNE PIG',
        provider: 'RECTANGLE',
        image: '/api/placeholder/120/120',
        tag: 'HOT',
        category: 'slots',
    },
    {
        id: 9,
        name: 'AZTEC GEMS',
        provider: 'PRAGMATIC PLAY',
        image: '/api/placeholder/120/120',
        tag: '',
        category: 'slots',
    },
    {
        id: 10,
        name: 'AVIATOR',
        provider: 'SPRIBE',
        image: '/api/placeholder/120/120',
        tag: 'TOP',
        category: 'original',
    },
    {
        id: 11,
        name: 'PROSPERITY RABBIT',
        provider: 'PG SOFT',
        image: '/api/placeholder/120/120',
        tag: '',
        category: 'slots',
    },
])

// 选择器选项
const categoryActions = ref([
    { name: 'Casino', value: 'casino' },
    { name: 'Sports', value: 'sports' },
    { name: 'Live Casino', value: 'live' },
])

const sortActions = ref([
    { name: 'Popular', value: 'popular' },
    { name: 'Newest', value: 'newest' },
    { name: 'A-Z', value: 'az' },
    { name: 'Z-A', value: 'za' },
])

const providerActions = ref([
    { name: 'All', value: 'all' },
    { name: 'TADA', value: 'tada' },
    { name: 'RECTANGLE', value: 'rectangle' },
    { name: 'PG SOFT', value: 'pgsoft' },
    { name: 'PRAGMATIC PLAY', value: 'pragmatic' },
    { name: 'SPRIBE', value: 'spribe' },
])

// 计算属性：过滤后的游戏列表
const filteredGames = computed(() => {
    let filtered = gamesList.value

    // 按分类过滤
    if (selectedCategory.value !== 'all') {
        filtered = filtered.filter((game) => game.category === selectedCategory.value)
    }

    // 按搜索关键词过滤
    if (searchKeyword.value) {
        filtered = filtered.filter(
            (game) =>
                game.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
                game.provider.toLowerCase().includes(searchKeyword.value.toLowerCase())
        )
    }

    // 按供应商过滤
    if (provider.value !== 'All') {
        filtered = filtered.filter((game) => game.provider.toLowerCase().includes(provider.value.toLowerCase()))
    }

    return filtered
})

// 方法
const showSearchPopup = () => {
    isSearchVisible.value = true
}

const closeSearchPopup = () => {
    isSearchVisible.value = false
}

const handleSearchInput = (value: string) => {
    searchKeyword.value = value
}

const performSearch = () => {
    console.log('执行搜索:', searchKeyword.value)
}

const selectCategory = (categoryId: string) => {
    selectedCategory.value = categoryId
}

const selectGame = (game: any) => {
    console.log('选择游戏:', game)
    // 这里可以添加游戏选择逻辑
}

const onCategorySelect = (action: any) => {
    console.log('选择分类:', action)
    showCategorySheet.value = false
}

const onSortSelect = (action: any) => {
    sortBy.value = action.name
    showSortSheet.value = false
}

const onProviderSelect = (action: any) => {
    provider.value = action.name
    showProviderSheet.value = false
}
</script>

<style lang="scss" scoped>
.search-game-container {
    width: 100%;
}

.search-popup {
    background: #2a2a2a;
    height: 100%;
    display: flex;
    flex-direction: column;
    color: #fff;

    .search-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        background: #333;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);

        .header-left {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            :deep(.van-icon) {
                color: #fff;
            }
        }

        .header-title {
            font-size: 18px;
            font-weight: 600;
            color: #fff;
        }

        .header-right {
            width: 40px;
        }
    }

    .search-bar {
        padding: 12px 16px;
        background: #333;

        .search-input-wrapper {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            overflow: hidden;

            :deep(.van-field) {
                background: transparent;

                .van-field__control {
                    color: #fff;
                    font-size: 14px;

                    &::placeholder {
                        color: rgba(255, 255, 255, 0.6);
                    }
                }

                .van-field__left-icon {
                    margin-right: 8px;
                }

                .van-field__right-icon {
                    margin-left: 8px;

                    :deep(.van-icon) {
                        color: rgba(255, 255, 255, 0.8);
                    }
                }
            }

            .category-selector {
                display: flex;
                align-items: center;
                gap: 4px;
                color: #fff;
                font-size: 14px;
                cursor: pointer;
                padding: 8px 12px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 6px;
                margin-right: 8px;

                :deep(.van-icon) {
                    color: rgba(255, 255, 255, 0.8);
                }
            }
        }
    }

    .game-categories {
        display: flex;
        gap: 8px;
        padding: 16px;
        overflow-x: auto;

        .category-tag {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.3s;

            &.active {
                background: #007aff;
                color: #fff;
            }

            :deep(.van-icon) {
                color: currentColor;
            }

            span {
                font-size: 14px;
            }
        }
    }

    .filters {
        display: flex;
        gap: 12px;
        padding: 0 16px 16px;

        .filter-item {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 8px;

            .filter-label {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.8);
                white-space: nowrap;
            }

            :deep(.van-field) {
                flex: 1;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 6px;

                .van-field__control {
                    color: #fff;
                    font-size: 14px;
                }

                .van-field__right-icon {
                    :deep(.van-icon) {
                        color: rgba(255, 255, 255, 0.6);
                    }
                }
            }
        }
    }

    .games-grid {
        flex: 1;
        padding: 0 16px 16px;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
        overflow-y: auto;

        .game-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.2s;

            &:hover {
                transform: scale(1.02);
            }

            .game-image {
                position: relative;
                aspect-ratio: 1;
                overflow: hidden;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }

                .game-tag {
                    position: absolute;
                    top: 6px;
                    right: 6px;
                    padding: 2px 8px;
                    border-radius: 12px;
                    font-size: 10px;
                    font-weight: 600;
                    text-transform: uppercase;

                    &.hot {
                        background: #ff4757;
                        color: #fff;
                    }

                    &.new {
                        background: #ff6b35;
                        color: #fff;
                    }

                    &.top {
                        background: #ffa502;
                        color: #fff;
                    }
                }
            }

            .game-info {
                padding: 8px;

                .game-name {
                    font-size: 12px;
                    font-weight: 600;
                    color: #fff;
                    margin-bottom: 4px;
                    text-align: center;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .game-provider {
                    font-size: 10px;
                    color: rgba(255, 255, 255, 0.6);
                    text-align: center;
                    text-transform: uppercase;
                }
            }
        }
    }
}

// 滚动条样式
:deep(.games-grid::-webkit-scrollbar) {
    width: 4px;
}

:deep(.games-grid::-webkit-scrollbar-track) {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
}

:deep(.games-grid::-webkit-scrollbar-thumb) {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

:deep(.games-grid::-webkit-scrollbar-thumb:hover) {
    background: rgba(255, 255, 255, 0.5);
}
</style>

<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 15:59:30
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-29 16:27:55
 * @FilePath     : /src/components/searchGame/index.vue
 * @Description  : 游戏搜索页面内容组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 15:59:30
-->

<template>
    <div class="search-game-container"></div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 定义事件
interface Emits {
    (e: 'close'): void
}

const emit = defineEmits<Emits>()

// 搜索相关状态
const searchKeyword = ref('')

// 分类和筛选状态
const selectedCategory = ref('all')
const sortBy = ref('Popular')
const provider = ref('All')

// 弹窗状态
const showCategorySheet = ref(false)
const showSortSheet = ref(false)
const showProviderSheet = ref(false)

// 游戏分类数据
const gameCategories = ref([
    { id: 'all', name: 'All games', icon: 'apps-o' },
    { id: 'original', name: 'Original', icon: 'star-o' },
    { id: 'slots', name: 'Slots', icon: 'gem-o' },
    { id: 'live', name: 'Live', icon: 'video-o' },
    { id: 'lottery', name: 'Lottery', icon: 'gift-o' },
])

// 模拟游戏数据
const gamesList = ref([
    {
        id: 1,
        name: 'LUCKY GAME',
        provider: 'RECTANGLE',
        image: '/api/placeholder/120/120',
        tag: 'HOT',
        category: 'slots',
    },
    {
        id: 2,
        name: 'FORTUNE GEMS',
        provider: 'TADA',
        image: '/api/placeholder/120/120',
        tag: 'NEW',
        category: 'slots',
    },
    {
        id: 3,
        name: 'BINGO',
        provider: 'JILI',
        image: '/api/placeholder/120/120',
        tag: 'NEW',
        category: 'lottery',
    },
    {
        id: 4,
        name: 'SWAGGY GAME',
        provider: 'RECTANGLE',
        image: '/api/placeholder/120/120',
        tag: 'HOT',
        category: 'slots',
    },
    {
        id: 5,
        name: 'MINES2',
        provider: 'AFUN MX',
        image: '/api/placeholder/120/120',
        tag: 'TOP',
        category: 'original',
    },
    {
        id: 6,
        name: 'FORTUNE',
        provider: 'PG SOFT',
        image: '/api/placeholder/120/120',
        tag: 'TOP',
        category: 'slots',
    },
    {
        id: 7,
        name: 'FORTUNE GEMS',
        provider: 'TADA',
        image: '/api/placeholder/120/120',
        tag: 'HOT',
        category: 'slots',
    },
    {
        id: 8,
        name: 'FORTUNE PIG',
        provider: 'RECTANGLE',
        image: '/api/placeholder/120/120',
        tag: 'HOT',
        category: 'slots',
    },
    {
        id: 9,
        name: 'AZTEC GEMS',
        provider: 'PRAGMATIC PLAY',
        image: '/api/placeholder/120/120',
        tag: '',
        category: 'slots',
    },
    {
        id: 10,
        name: 'AVIATOR',
        provider: 'SPRIBE',
        image: '/api/placeholder/120/120',
        tag: 'TOP',
        category: 'original',
    },
    {
        id: 11,
        name: 'PROSPERITY RABBIT',
        provider: 'PG SOFT',
        image: '/api/placeholder/120/120',
        tag: '',
        category: 'slots',
    },
])

// 选择器选项
const categoryActions = ref([
    { name: 'Casino', value: 'casino' },
    { name: 'Sports', value: 'sports' },
    { name: 'Live Casino', value: 'live' },
])

const sortActions = ref([
    { name: 'Popular', value: 'popular' },
    { name: 'Newest', value: 'newest' },
    { name: 'A-Z', value: 'az' },
    { name: 'Z-A', value: 'za' },
])

const providerActions = ref([
    { name: 'All', value: 'all' },
    { name: 'TADA', value: 'tada' },
    { name: 'RECTANGLE', value: 'rectangle' },
    { name: 'PG SOFT', value: 'pgsoft' },
    { name: 'PRAGMATIC PLAY', value: 'pragmatic' },
    { name: 'SPRIBE', value: 'spribe' },
])

// 计算属性：过滤后的游戏列表
const filteredGames = computed(() => {
    let filtered = gamesList.value

    // 按分类过滤
    if (selectedCategory.value !== 'all') {
        filtered = filtered.filter((game) => game.category === selectedCategory.value)
    }

    // 按搜索关键词过滤
    if (searchKeyword.value) {
        filtered = filtered.filter(
            (game) =>
                game.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
                game.provider.toLowerCase().includes(searchKeyword.value.toLowerCase())
        )
    }

    // 按供应商过滤
    if (provider.value !== 'All') {
        filtered = filtered.filter((game) => game.provider.toLowerCase().includes(provider.value.toLowerCase()))
    }

    return filtered
})

// 方法
const handleClose = () => {
    emit('close')
}

const handleSearchInput = (value: string) => {
    searchKeyword.value = value
}

const performSearch = () => {
    console.log('执行搜索:', searchKeyword.value)
}

const selectCategory = (categoryId: string) => {
    selectedCategory.value = categoryId
}

const selectGame = (game: any) => {
    console.log('选择游戏:', game)
    // 这里可以添加游戏选择逻辑
}

const onCategorySelect = (action: any) => {
    console.log('选择分类:', action)
    showCategorySheet.value = false
}

const onSortSelect = (action: any) => {
    sortBy.value = action.name
    showSortSheet.value = false
}

const onProviderSelect = (action: any) => {
    provider.value = action.name
    showProviderSheet.value = false
}
</script>

<style lang="scss" scoped>
.search-popup {
    background: #2a2a2a;
    height: 100%;
    display: flex;
    flex-direction: column;
    color: #fff;

    .search-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        background: #333;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);

        .header-left {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            :deep(.van-icon) {
                color: #fff;
            }
        }

        .header-title {
            font-size: 18px;
            font-weight: 600;
            color: #fff;
        }

        .header-right {
            width: 40px;
        }
    }

    .search-bar {
        padding: 12px 16px;
        background: #333;

        .search-input-wrapper {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            overflow: hidden;

            :deep(.van-field) {
                background: transparent;

                .van-field__control {
                    color: #fff;
                    font-size: 14px;

                    &::placeholder {
                        color: rgba(255, 255, 255, 0.6);
                    }
                }

                .van-field__left-icon {
                    margin-right: 8px;
                }

                .van-field__right-icon {
                    margin-left: 8px;

                    :deep(.van-icon) {
                        color: rgba(255, 255, 255, 0.8);
                    }
                }
            }

            .category-selector {
                display: flex;
                align-items: center;
                gap: 4px;
                color: #fff;
                font-size: 14px;
                cursor: pointer;
                padding: 8px 12px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 6px;
                margin-right: 8px;

                :deep(.van-icon) {
                    color: rgba(255, 255, 255, 0.8);
                }
            }
        }
    }

    .game-categories {
        display: flex;
        gap: 8px;
        padding: 16px;
        overflow-x: auto;

        .category-tag {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.3s;

            &.active {
                background: #007aff;
                color: #fff;
            }

            :deep(.van-icon) {
                color: currentColor;
            }

            span {
                font-size: 14px;
            }
        }
    }

    .filters {
        display: flex;
        gap: 12px;
        padding: 0 16px 16px;

        .filter-item {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 8px;

            .filter-label {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.8);
                white-space: nowrap;
            }

            :deep(.van-field) {
                flex: 1;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 6px;

                .van-field__control {
                    color: #fff;
                    font-size: 14px;
                }

                .van-field__right-icon {
                    :deep(.van-icon) {
                        color: rgba(255, 255, 255, 0.6);
                    }
                }
            }
        }
    }

    .games-grid {
        flex: 1;
        padding: 0 16px 16px;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
        overflow-y: auto;

        .game-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.2s;

            &:hover {
                transform: scale(1.02);
            }

            .game-image {
                position: relative;
                aspect-ratio: 1;
                overflow: hidden;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }

                .game-tag {
                    position: absolute;
                    top: 6px;
                    right: 6px;
                    padding: 2px 8px;
                    border-radius: 12px;
                    font-size: 10px;
                    font-weight: 600;
                    text-transform: uppercase;

                    &.hot {
                        background: #ff4757;
                        color: #fff;
                    }

                    &.new {
                        background: #ff6b35;
                        color: #fff;
                    }

                    &.top {
                        background: #ffa502;
                        color: #fff;
                    }
                }
            }

            .game-info {
                padding: 8px;

                .game-name {
                    font-size: 12px;
                    font-weight: 600;
                    color: #fff;
                    margin-bottom: 4px;
                    text-align: center;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .game-provider {
                    font-size: 10px;
                    color: rgba(255, 255, 255, 0.6);
                    text-align: center;
                    text-transform: uppercase;
                }
            }
        }
    }
}

// 滚动条样式
:deep(.games-grid::-webkit-scrollbar) {
    width: 4px;
}

:deep(.games-grid::-webkit-scrollbar-track) {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
}

:deep(.games-grid::-webkit-scrollbar-thumb) {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

:deep(.games-grid::-webkit-scrollbar-thumb:hover) {
    background: rgba(255, 255, 255, 0.5);
}
</style>

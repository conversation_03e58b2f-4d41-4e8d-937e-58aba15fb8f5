<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 15:59:30
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-29 16:54:52
 * @FilePath     : /src/components/searchGame/index.vue
 * @Description  : 游戏搜索页面内容组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 15:59:30
-->

<template>
    <div class="search-game-container">
        <!-- 1. 导航条 -->
        <SearchHeader @close="handleClose" />

        <!-- 2. 搜索栏 -->
        <SearchBar
            v-model="searchKeyword"
            :selected-category="selectedCategoryName"
            @search="performSearch"
            @category-click="showCategorySheet = true"
        />

        <!-- 3. 分类标签 -->
        <GameCategories :selected-category="selectedCategory" :categories="gameCategories" @category-select="selectCategory" />

        <!-- 4. 排序筛选器 -->
        <GameFilters
            v-model:sort-by="sortBy"
            v-model:provider="provider"
            @sort-click="showSortSheet = true"
            @provider-click="showProviderSheet = true"
        />

        <!-- 5. 游戏列表 -->
        <GameGrid :games="filteredGames" @game-select="selectGame" />

        <!-- 分类选择器 -->
        <van-action-sheet v-model:show="showCategorySheet" :actions="categoryActions" @select="onCategorySelect" />

        <!-- 排序选择器 -->
        <van-action-sheet v-model:show="showSortSheet" :actions="sortActions" @select="onSortSelect" />

        <!-- 供应商选择器 -->
        <van-action-sheet v-model:show="showProviderSheet" :actions="providerActions" @select="onProviderSelect" />
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import SearchHeader from './SearchHeader.vue'
import SearchBar from './SearchBar.vue'
import GameCategories from './GameCategories.vue'
import GameFilters from './GameFilters.vue'
import GameGrid from './GameGrid.vue'

// 定义事件
interface Emits {
    (e: 'close'): void
}

const emit = defineEmits<Emits>()

// 搜索相关状态
const searchKeyword = ref('')

// 分类和筛选状态
const selectedCategory = ref('all')
const sortBy = ref('Popular')
const provider = ref('All')

// 弹窗状态
const showCategorySheet = ref(false)
const showSortSheet = ref(false)
const showProviderSheet = ref(false)

// 游戏分类数据
const gameCategories = ref([
    { id: 'all', name: 'All games', icon: 'apps-o' },
    { id: 'original', name: 'Original', icon: 'star-o' },
    { id: 'slots', name: 'Slots', icon: 'gem-o' },
    { id: 'live', name: 'Live', icon: 'video-o' },
    { id: 'lottery', name: 'Lottery', icon: 'gift-o' },
])

// 模拟游戏数据
const gamesList = ref([
    {
        id: 1,
        name: 'LUCKY GAME',
        provider: 'RECTANGLE',
        image: '/api/placeholder/120/120',
        tag: 'HOT',
        category: 'slots',
    },
    {
        id: 2,
        name: 'FORTUNE GEMS',
        provider: 'TADA',
        image: '/api/placeholder/120/120',
        tag: 'NEW',
        category: 'slots',
    },
    {
        id: 3,
        name: 'BINGO',
        provider: 'JILI',
        image: '/api/placeholder/120/120',
        tag: 'NEW',
        category: 'lottery',
    },
    {
        id: 4,
        name: 'SWAGGY GAME',
        provider: 'RECTANGLE',
        image: '/api/placeholder/120/120',
        tag: 'HOT',
        category: 'slots',
    },
    {
        id: 5,
        name: 'MINES2',
        provider: 'AFUN MX',
        image: '/api/placeholder/120/120',
        tag: 'TOP',
        category: 'original',
    },
    {
        id: 6,
        name: 'FORTUNE',
        provider: 'PG SOFT',
        image: '/api/placeholder/120/120',
        tag: 'TOP',
        category: 'slots',
    },
    {
        id: 7,
        name: 'FORTUNE GEMS',
        provider: 'TADA',
        image: '/api/placeholder/120/120',
        tag: 'HOT',
        category: 'slots',
    },
    {
        id: 8,
        name: 'FORTUNE PIG',
        provider: 'RECTANGLE',
        image: '/api/placeholder/120/120',
        tag: 'HOT',
        category: 'slots',
    },
    {
        id: 9,
        name: 'AZTEC GEMS',
        provider: 'PRAGMATIC PLAY',
        image: '/api/placeholder/120/120',
        tag: '',
        category: 'slots',
    },
    {
        id: 10,
        name: 'AVIATOR',
        provider: 'SPRIBE',
        image: '/api/placeholder/120/120',
        tag: 'TOP',
        category: 'original',
    },
    {
        id: 11,
        name: 'PROSPERITY RABBIT',
        provider: 'PG SOFT',
        image: '/api/placeholder/120/120',
        tag: '',
        category: 'slots',
    },
])

// 选择器选项
const categoryActions = ref([
    { name: 'Casino', value: 'casino' },
    { name: 'Sports', value: 'sports' },
    { name: 'Live Casino', value: 'live' },
])

const sortActions = ref([
    { name: 'Popular', value: 'popular' },
    { name: 'Newest', value: 'newest' },
    { name: 'A-Z', value: 'az' },
    { name: 'Z-A', value: 'za' },
])

const providerActions = ref([
    { name: 'All', value: 'all' },
    { name: 'TADA', value: 'tada' },
    { name: 'RECTANGLE', value: 'rectangle' },
    { name: 'PG SOFT', value: 'pgsoft' },
    { name: 'PRAGMATIC PLAY', value: 'pragmatic' },
    { name: 'SPRIBE', value: 'spribe' },
])

// 计算属性：选中分类的名称
const selectedCategoryName = computed(() => {
    const category = gameCategories.value.find((cat) => cat.id === selectedCategory.value)
    return category ? category.name : 'Casino'
})

// 计算属性：过滤后的游戏列表
const filteredGames = computed(() => {
    let filtered = gamesList.value

    // 按分类过滤
    if (selectedCategory.value !== 'all') {
        filtered = filtered.filter((game) => game.category === selectedCategory.value)
    }

    // 按搜索关键词过滤
    if (searchKeyword.value) {
        filtered = filtered.filter(
            (game) =>
                game.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
                game.provider.toLowerCase().includes(searchKeyword.value.toLowerCase())
        )
    }

    // 按供应商过滤
    if (provider.value !== 'All') {
        filtered = filtered.filter((game) => game.provider.toLowerCase().includes(provider.value.toLowerCase()))
    }

    return filtered
})

// 方法
const handleClose = () => {
    emit('close')
}

const performSearch = () => {
    console.log('执行搜索:', searchKeyword.value)
}

const selectCategory = (categoryId: string) => {
    selectedCategory.value = categoryId
}

const selectGame = (game: any) => {
    console.log('选择游戏:', game)
    // 这里可以添加游戏选择逻辑
}

const onCategorySelect = (action: any) => {
    console.log('选择分类:', action)
    showCategorySheet.value = false
}

const onSortSelect = (action: any) => {
    sortBy.value = action.name
    showSortSheet.value = false
}

const onProviderSelect = (action: any) => {
    provider.value = action.name
    showProviderSheet.value = false
}
</script>

<style lang="scss" scoped>
$bg-primary: #2b2b2b;
$text-primary: #ffffff;

.search-game-container {
    background: $bg-primary;
    height: 100vh;
    display: flex;
    flex-direction: column;
    color: $text-primary;
    overflow: hidden;
}
</style>

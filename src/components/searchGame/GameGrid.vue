<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 16:54:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-29 16:54:00
 * @FilePath     : /src/components/searchGame/GameGrid.vue
 * @Description  : 游戏网格列表组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 16:54:00
-->

<template>
    <div class="games-grid">
        <div 
            v-for="game in games" 
            :key="game.id"
            class="game-item"
            @click="handleGameSelect(game)"
        >
            <div class="game-image">
                <img :src="game.image" :alt="game.name" />
                <div v-if="game.tag" :class="['game-tag', game.tag.toLowerCase()]">
                    {{ game.tag }}
                </div>
            </div>
            <div class="game-info">
                <div class="game-name">{{ game.name }}</div>
                <div class="game-provider">{{ game.provider }}</div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
// 定义游戏数据类型
interface Game {
    id: number
    name: string
    provider: string
    image: string
    tag: string
    category: string
}

// 定义属性
interface Props {
    games?: Game[]
}

// 定义事件
interface Emits {
    (e: 'game-select', game: Game): void
}

const props = withDefaults(defineProps<Props>(), {
    games: () => []
})

const emit = defineEmits<Emits>()

const handleGameSelect = (game: Game) => {
    emit('game-select', game)
}
</script>

<style lang="scss" scoped>
$text-primary: #ffffff;

.games-grid {
    flex: 1;
    padding: 0 16px 16px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    overflow-y: auto;

    .game-item {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        overflow: hidden;
        cursor: pointer;
        transition: transform 0.2s;

        &:hover {
            transform: scale(1.02);
        }

        .game-image {
            position: relative;
            aspect-ratio: 1;
            overflow: hidden;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

            .game-tag {
                position: absolute;
                top: 6px;
                right: 6px;
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 10px;
                font-weight: 600;
                text-transform: uppercase;

                &.hot {
                    background: #ff4757;
                    color: #fff;
                }

                &.new {
                    background: #ff6b35;
                    color: #fff;
                }

                &.top {
                    background: #ffa502;
                    color: #fff;
                }
            }
        }

        .game-info {
            padding: 8px;

            .game-name {
                font-size: 12px;
                font-weight: 600;
                color: $text-primary;
                margin-bottom: 4px;
                text-align: center;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .game-provider {
                font-size: 10px;
                color: rgba(255, 255, 255, 0.6);
                text-align: center;
                text-transform: uppercase;
            }
        }
    }
}

// 滚动条样式
:deep(.games-grid::-webkit-scrollbar) {
    width: 4px;
}

:deep(.games-grid::-webkit-scrollbar-track) {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
}

:deep(.games-grid::-webkit-scrollbar-thumb) {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

:deep(.games-grid::-webkit-scrollbar-thumb:hover) {
    background: rgba(255, 255, 255, 0.5);
}
</style>

<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 16:53:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-29 16:53:00
 * @FilePath     : /src/components/searchGame/GameFilters.vue
 * @Description  : 游戏筛选器组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 16:53:00
-->

<template>
    <div class="filters">
        <div class="filter-item">
            <span class="filter-label">Sort by:</span>
            <van-field
                v-model="sortBy"
                readonly
                is-link
                arrow-direction="down"
                @click="handleSortClick"
            />
        </div>
        <div class="filter-item">
            <span class="filter-label">Providers:</span>
            <van-field
                v-model="provider"
                readonly
                is-link
                arrow-direction="down"
                @click="handleProviderClick"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

// 定义属性
interface Props {
    sortBy?: string
    provider?: string
}

// 定义事件
interface Emits {
    (e: 'sort-click'): void
    (e: 'provider-click'): void
    (e: 'update:sortBy', value: string): void
    (e: 'update:provider', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
    sortBy: 'Popular',
    provider: 'All'
})

const emit = defineEmits<Emits>()

const sortBy = ref(props.sortBy)
const provider = ref(props.provider)

const handleSortClick = () => {
    emit('sort-click')
}

const handleProviderClick = () => {
    emit('provider-click')
}

// 监听外部值变化
watch(() => props.sortBy, (newValue) => {
    sortBy.value = newValue
})

watch(() => props.provider, (newValue) => {
    provider.value = newValue
})

// 监听内部值变化，同步到外部
watch(sortBy, (newValue) => {
    emit('update:sortBy', newValue)
})

watch(provider, (newValue) => {
    emit('update:provider', newValue)
})
</script>

<style lang="scss" scoped>
$text-primary: #ffffff;

.filters {
    display: flex;
    gap: 12px;
    padding: 0 16px 16px;
    flex-shrink: 0;

    .filter-item {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 8px;

        .filter-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            white-space: nowrap;
        }

        :deep(.van-field) {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;

            .van-field__control {
                color: $text-primary;
                font-size: 14px;
            }

            .van-field__right-icon {
                :deep(.van-icon) {
                    color: rgba(255, 255, 255, 0.6);
                }
            }
        }
    }
}
</style>

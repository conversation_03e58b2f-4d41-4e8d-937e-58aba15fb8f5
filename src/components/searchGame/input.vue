<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 15:56:33
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-29 16:20:10
 * @FilePath     : /src/components/searchGame/input.vue
 * @Description  : 游戏搜索输入框组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 15:56:33
-->

<template>
    <div class="search-game-input">
        <van-search
            v-model="searchValue"
            :placeholder="$t('Search_games')"
            :show-action="false"
            shape="round"
            background="transparent"
            @search="handleSearch"
            @input="handleInput"
            @focus="handleFocus"
            @blur="handleBlur"
        />

        <!-- 搜索弹窗 -->
        <van-popup v-model:show="isSearchVisible" position="right" :style="{ width: '100%', height: '100%' }" :close-on-click-overlay="false">
            <div class="search-popup">
                <!-- 头部 -->
                <div class="search-header">
                    <div class="header-left" @click="closeSearchPopup">
                        <van-icon name="arrow-left" size="20" />
                    </div>
                    <div class="header-title">Search</div>
                    <div class="header-right"></div>
                </div>

                <!-- 搜索栏 -->
                <div class="search-bar">
                    <div class="search-input-wrapper">
                        <van-field v-model="searchKeyword" placeholder="Game Name" :border="false" @input="handleSearchInput">
                            <template #left-icon>
                                <span class="category-selector" @click="showCategorySheet = true">
                                    Casino
                                    <van-icon name="arrow-down" size="12" />
                                </span>
                            </template>
                            <template #right-icon>
                                <van-icon name="search" size="18" @click="performSearch" />
                            </template>
                        </van-field>
                    </div>
                </div>

                <!-- 游戏分类标签 -->
                <div class="game-categories">
                    <div
                        v-for="category in gameCategories"
                        :key="category.id"
                        :class="['category-tag', { active: selectedCategory === category.id }]"
                        @click="selectCategory(category.id)"
                    >
                        <van-icon :name="category.icon" size="16" />
                        <span>{{ category.name }}</span>
                    </div>
                </div>

                <!-- 筛选器 -->
                <div class="filters">
                    <div class="filter-item">
                        <span class="filter-label">Sort by:</span>
                        <van-field v-model="sortBy" readonly is-link arrow-direction="down" @click="showSortSheet = true" />
                    </div>
                    <div class="filter-item">
                        <span class="filter-label">Providers:</span>
                        <van-field v-model="provider" readonly is-link arrow-direction="down" @click="showProviderSheet = true" />
                    </div>
                </div>

                <!-- 游戏列表 -->
                <div class="games-grid">
                    <div v-for="game in filteredGames" :key="game.id" class="game-item" @click="selectGame(game)">
                        <div class="game-image">
                            <img :src="game.image" :alt="game.name" />
                            <div v-if="game.tag" :class="['game-tag', game.tag.toLowerCase()]">
                                {{ game.tag }}
                            </div>
                        </div>
                        <div class="game-info">
                            <div class="game-name">{{ game.name }}</div>
                            <div class="game-provider">{{ game.provider }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </van-popup>

        <!-- 分类选择器 -->
        <van-action-sheet v-model:show="showCategorySheet" :actions="categoryActions" @select="onCategorySelect" />

        <!-- 排序选择器 -->
        <van-action-sheet v-model:show="showSortSheet" :actions="sortActions" @select="onSortSelect" />

        <!-- 供应商选择器 -->
        <van-action-sheet v-model:show="showProviderSheet" :actions="providerActions" @select="onProviderSelect" />
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 定义组件属性
interface Props {
    modelValue?: string
    placeholder?: string
}

// 定义事件
interface Emits {
    (e: 'update:modelValue', value: string): void
    (e: 'search', value: string): void
    (e: 'input', value: string): void
    (e: 'focus', event: Event): void
    (e: 'blur', event: Event): void
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: '',
    placeholder: 'Search_games',
})

const emit = defineEmits<Emits>()

// 搜索相关状态
const searchValue = ref(props.modelValue)
const searchKeyword = ref('')
const isSearchVisible = ref(false)

// 分类和筛选状态
const selectedCategory = ref('all')
const sortBy = ref('Popular')
const provider = ref('All')

// 弹窗状态
const showCategorySheet = ref(false)
const showSortSheet = ref(false)
const showProviderSheet = ref(false)

// 游戏分类数据
const gameCategories = ref([
    { id: 'all', name: 'All games', icon: 'apps-o' },
    { id: 'original', name: 'Original', icon: 'star-o' },
    { id: 'slots', name: 'Slots', icon: 'gem-o' },
    { id: 'live', name: 'Live', icon: 'video-o' },
    { id: 'lottery', name: 'Lottery', icon: 'gift-o' },
])

// 模拟游戏数据
const gamesList = ref([
    {
        id: 1,
        name: 'LUCKY GAME',
        provider: 'RECTANGLE',
        image: '/api/placeholder/120/120',
        tag: 'HOT',
        category: 'slots',
    },
    {
        id: 2,
        name: 'FORTUNE GEMS',
        provider: 'TADA',
        image: '/api/placeholder/120/120',
        tag: 'NEW',
        category: 'slots',
    },
    {
        id: 3,
        name: 'BINGO',
        provider: 'JILI',
        image: '/api/placeholder/120/120',
        tag: 'NEW',
        category: 'lottery',
    },
    {
        id: 4,
        name: 'SWAGGY GAME',
        provider: 'RECTANGLE',
        image: '/api/placeholder/120/120',
        tag: 'HOT',
        category: 'slots',
    },
    {
        id: 5,
        name: 'MINES2',
        provider: 'AFUN MX',
        image: '/api/placeholder/120/120',
        tag: 'TOP',
        category: 'original',
    },
    {
        id: 6,
        name: 'FORTUNE',
        provider: 'PG SOFT',
        image: '/api/placeholder/120/120',
        tag: 'TOP',
        category: 'slots',
    },
    {
        id: 7,
        name: 'FORTUNE GEMS',
        provider: 'TADA',
        image: '/api/placeholder/120/120',
        tag: 'HOT',
        category: 'slots',
    },
    {
        id: 8,
        name: 'FORTUNE PIG',
        provider: 'RECTANGLE',
        image: '/api/placeholder/120/120',
        tag: 'HOT',
        category: 'slots',
    },
    {
        id: 9,
        name: 'AZTEC GEMS',
        provider: 'PRAGMATIC PLAY',
        image: '/api/placeholder/120/120',
        tag: '',
        category: 'slots',
    },
    {
        id: 10,
        name: 'AVIATOR',
        provider: 'SPRIBE',
        image: '/api/placeholder/120/120',
        tag: 'TOP',
        category: 'original',
    },
    {
        id: 11,
        name: 'PROSPERITY RABBIT',
        provider: 'PG SOFT',
        image: '/api/placeholder/120/120',
        tag: '',
        category: 'slots',
    },
])

// 选择器选项
const categoryActions = ref([
    { name: 'Casino', value: 'casino' },
    { name: 'Sports', value: 'sports' },
    { name: 'Live Casino', value: 'live' },
])

const sortActions = ref([
    { name: 'Popular', value: 'popular' },
    { name: 'Newest', value: 'newest' },
    { name: 'A-Z', value: 'az' },
    { name: 'Z-A', value: 'za' },
])

const providerActions = ref([
    { name: 'All', value: 'all' },
    { name: 'TADA', value: 'tada' },
    { name: 'RECTANGLE', value: 'rectangle' },
    { name: 'PG SOFT', value: 'pgsoft' },
    { name: 'PRAGMATIC PLAY', value: 'pragmatic' },
    { name: 'SPRIBE', value: 'spribe' },
])

// 计算属性：过滤后的游戏列表
const filteredGames = computed(() => {
    let filtered = gamesList.value

    // 按分类过滤
    if (selectedCategory.value !== 'all') {
        filtered = filtered.filter((game) => game.category === selectedCategory.value)
    }

    // 按搜索关键词过滤
    if (searchKeyword.value) {
        filtered = filtered.filter(
            (game) =>
                game.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
                game.provider.toLowerCase().includes(searchKeyword.value.toLowerCase())
        )
    }

    // 按供应商过滤
    if (provider.value !== 'All') {
        filtered = filtered.filter((game) => game.provider.toLowerCase().includes(provider.value.toLowerCase()))
    }

    return filtered
})

// 监听搜索值变化，同步到父组件
watch(
    () => props.modelValue,
    (newValue) => {
        searchValue.value = newValue
    }
)

watch(searchValue, (newValue) => {
    emit('update:modelValue', newValue)
})

// 方法
const showSearchPopup = () => {
    isSearchVisible.value = true
}

const closeSearchPopup = () => {
    isSearchVisible.value = false
}

const handleSearchInput = (value: string) => {
    searchKeyword.value = value
}

const performSearch = () => {
    console.log('执行搜索:', searchKeyword.value)
}

const selectCategory = (categoryId: string) => {
    selectedCategory.value = categoryId
}

const selectGame = (game: any) => {
    console.log('选择游戏:', game)
    // 这里可以添加游戏选择逻辑
}

const onCategorySelect = (action: any) => {
    console.log('选择分类:', action)
    showCategorySheet.value = false
}

const onSortSelect = (action: any) => {
    sortBy.value = action.name
    showSortSheet.value = false
}

const onProviderSelect = (action: any) => {
    provider.value = action.name
    showProviderSheet.value = false
}

// 处理搜索事件
const handleSearch = (value: string) => {
    emit('search', value)
}

// 处理输入事件
const handleInput = (value: string) => {
    emit('input', value)
}

// 处理聚焦事件
const handleFocus = (event: Event) => {
    emit('focus', event)
    console.log('%c------ d 点击', 'background-color:blue;font-size:12px;color:#fff')
    // 显示搜索弹窗
    showSearchPopup()
}

// 处理失焦事件
const handleBlur = (event: Event) => {
    emit('blur', event)
}
</script>

<style lang="scss" scoped>
.search-game-input {
    width: 100%;
    padding: 0 15px;

    :deep(.van-search) {
        padding: 0;
        background: transparent;

        .van-search__content {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding-left: 12px;

            .van-field {
                background: transparent;

                .van-field__left-icon {
                    color: rgba(255, 255, 255, 0.6);
                    margin-right: 8px;
                }

                .van-field__control {
                    color: #ffffff;
                    font-size: 14px;

                    &::placeholder {
                        color: rgba(255, 255, 255, 0.6);
                    }
                }
            }
        }

        // 移除默认的边框
        .van-search__content::after {
            display: none;
        }
    }
}
</style>

<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 16:52:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-29 16:52:00
 * @FilePath     : /src/components/searchGame/GameCategories.vue
 * @Description  : 游戏分类标签组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 16:52:00
-->

<template>
    <div class="game-categories">
        <div 
            v-for="category in categories" 
            :key="category.id"
            :class="['category-tag', { active: selectedCategory === category.id }]"
            @click="handleCategorySelect(category.id)"
        >
            <van-icon :name="category.icon" size="16" />
            <span>{{ category.name }}</span>
        </div>
    </div>
</template>

<script setup lang="ts">
// 定义分类数据类型
interface Category {
    id: string
    name: string
    icon: string
}

// 定义属性
interface Props {
    selectedCategory?: string
    categories?: Category[]
}

// 定义事件
interface Emits {
    (e: 'category-select', categoryId: string): void
}

const props = withDefaults(defineProps<Props>(), {
    selectedCategory: 'all',
    categories: () => [
        { id: 'all', name: 'All games', icon: 'apps-o' },
        { id: 'original', name: 'Original', icon: 'star-o' },
        { id: 'slots', name: 'Slots', icon: 'gem-o' },
        { id: 'live', name: 'Live', icon: 'video-o' },
        { id: 'lottery', name: 'Lottery', icon: 'gift-o' }
    ]
})

const emit = defineEmits<Emits>()

const handleCategorySelect = (categoryId: string) => {
    emit('category-select', categoryId)
}
</script>

<style lang="scss" scoped>
$accent-color: #4caf50;
$text-primary: #ffffff;

.game-categories {
    display: flex;
    gap: 8px;
    padding: 16px;
    overflow-x: auto;
    flex-shrink: 0;

    .category-tag {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 8px 16px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        white-space: nowrap;
        cursor: pointer;
        transition: all 0.3s;

        &.active {
            background: $accent-color;
            color: $text-primary;
        }

        :deep(.van-icon) {
            color: currentColor;
        }

        span {
            font-size: 14px;
        }
    }
}
</style>

<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 16:51:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-29 16:51:00
 * @FilePath     : /src/components/searchGame/SearchBar.vue
 * @Description  : 搜索栏组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 16:51:00
-->

<template>
    <div class="search-bar">
        <div class="search-input-wrapper">
            <van-field v-model="searchKeyword" placeholder="Game Name" :border="false" @input="handleSearchInput">
                <template #left-icon>
                    <span class="category-selector" @click="handleCategoryClick">
                        {{ selectedCategory }}
                        <van-icon name="arrow-down" size="12" />
                    </span>
                </template>
                <template #right-icon>
                    <van-icon name="search" size="18" @click="handleSearch" />
                </template>
            </van-field>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

// 定义属性
interface Props {
    modelValue?: string
    selectedCategory?: string
}

// 定义事件
interface Emits {
    (e: 'update:modelValue', value: string): void
    (e: 'search', value: string): void
    (e: 'category-click'): void
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: '',
    selectedCategory: 'Casino',
})

const emit = defineEmits<Emits>()

const searchKeyword = ref(props.modelValue)

const handleSearchInput = (value: string) => {
    searchKeyword.value = value
    emit('update:modelValue', value)
}

const handleSearch = () => {
    emit('search', searchKeyword.value)
}

const handleCategoryClick = () => {
    emit('category-click')
}

// 监听外部值变化
watch(
    () => props.modelValue,
    (newValue) => {
        searchKeyword.value = newValue
    }
)
</script>

<style lang="scss" scoped>
$bg-secondary: #383838;
$text-primary: #ffffff;

.search-bar {
    padding: 12px 16px;
    background: $bg-secondary;
    flex-shrink: 0;

    .search-input-wrapper {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        overflow: hidden;

        :deep(.van-field) {
            background: transparent;

            .van-field__control {
                color: $text-primary;
                font-size: 14px;

                &::placeholder {
                    color: rgba(255, 255, 255, 0.6);
                }
            }

            .van-field__left-icon {
                margin-right: 8px;
            }

            .van-field__right-icon {
                margin-left: 8px;

                :deep(.van-icon) {
                    color: rgba(255, 255, 255, 0.8);
                }
            }
        }

        .category-selector {
            display: flex;
            align-items: center;
            gap: 4px;
            color: $text-primary;
            font-size: 14px;
            cursor: pointer;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            margin-right: 8px;

            :deep(.van-icon) {
                color: rgba(255, 255, 255, 0.8);
            }
        }
    }
}
</style>

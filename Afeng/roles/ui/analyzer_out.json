{"componentName": "RulesPopup", "containerStyles": {"display": "flex", "flexDirection": "column", "height": "100%", "width": "100%", "backgroundColor": "linear-gradient(180deg, rgb(24, 35, 53) 0%, rgb(17, 25, 39) 100%)", "borderRadius": "16px 16px 0 0", "overflow": "hidden"}, "layout": {"type": "flex", "direction": "column", "areas": [{"name": "headerSection", "className": "rules-header", "layout": "flex-horizontal", "elements": [{"type": "text", "name": "title", "className": "rules-title", "content": "Rules", "styles": {"fontSize": "20px", "fontWeight": "600", "color": "#ffffff", "margin": "0"}}, {"type": "button", "name": "closeButton", "className": "close-button", "styles": {"width": "32px", "height": "32px", "backgroundColor": "transparent", "border": "none", "borderRadius": "6px", "padding": "8px", "cursor": "pointer"}}]}, {"name": "contentSection", "className": "rules-content", "layout": "flex-vertical", "elements": [{"type": "container", "name": "rulesTextSection", "className": "rules-text-section", "styles": {"marginBottom": "24px"}}, {"type": "container", "name": "blessingsSection", "className": "blessings-section", "styles": {"textAlign": "center", "margin": "24px 0", "padding": "16px 0"}}, {"type": "container", "name": "prizeCalculationSection", "className": "prize-calculation-section", "styles": {"marginTop": "24px"}}]}]}, "colors": {"primary": "#17d400", "text": "#ffffff", "background": "linear-gradient(180deg, rgb(24, 35, 53) 0%, rgb(17, 25, 39) 100%)", "textSecondary": "rgba(255, 255, 255, 0.8)", "border": "rgba(255, 255, 255, 0.1)"}, "typography": {"fontFamily": "<PERSON>l, sans-serif", "sizes": {"sm": "14px", "md": "16px", "lg": "18px", "xl": "20px"}, "weights": {"normal": "400", "medium": "500", "semibold": "600"}}, "detailedElements": {"rulesMainTitle": {"type": "heading", "content": "Jackpot of The Day Rules", "styles": {"fontSize": "18px", "fontWeight": "600", "color": "#ffffff", "marginBottom": "16px", "marginTop": "0"}}, "rulesOrderedList": {"type": "container", "styles": {"color": "rgba(255, 255, 255, 0.8)", "lineHeight": "1.6", "marginBottom": "16px"}}, "blessingsText": {"type": "text", "content": "⭐⭐ Good luck and have fun! ⭐⭐", "styles": {"fontSize": "16px", "fontWeight": "500", "color": "#17d400", "textAlign": "center"}}, "prizeFormulaTitle": {"type": "heading", "content": "Prize Calculation Formula", "styles": {"fontSize": "18px", "fontWeight": "600", "color": "#ffffff", "marginBottom": "16px", "marginTop": "0"}}, "prizeCalculationList": {"type": "container", "className": "prize-calculation-list", "styles": {"marginTop": "8px", "marginLeft": "12px"}}, "prizeListItem": {"type": "text", "styles": {"whiteSpace": "nowrap", "marginBottom": "8px", "color": "rgba(255, 255, 255, 0.8)", "fontSize": "14px", "lineHeight": "1.6"}}, "highlightPlace": {"type": "text", "styles": {"color": "#17d400", "fontWeight": "500"}}, "highlightPercentage": {"type": "text", "styles": {"color": "#17d400", "fontWeight": "500"}}, "closeIcon": {"type": "icon", "styles": {"width": "24px", "height": "24px", "fill": "#ffffff"}}}, "scrollStyles": {"contentArea": {"overflowY": "auto", "flex": "1", "padding": "20px 30px"}, "customScrollbar": {"width": "6px", "backgroundColor": "rgba(255, 255, 255, 0.1)", "borderRadius": "3px"}, "scrollbarThumb": {"backgroundColor": "rgba(255, 255, 255, 0.3)", "borderRadius": "3px"}}, "responsiveBreakpoints": {"mobile": {"maxWidth": "768px", "adjustments": {"headerPadding": "15px 20px", "contentPadding": "15px 20px", "fontSize": {"title": "18px", "heading": "16px", "body": "13px"}}}}, "interactions": {"closeButton": {"hover": {"backgroundColor": "rgba(255, 255, 255, 0.1)"}, "active": {"transform": "scale(0.95)"}}}}